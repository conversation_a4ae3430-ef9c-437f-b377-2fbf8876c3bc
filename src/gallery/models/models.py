import io
import json
import logging
import math

from collections import defaultdict
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from functools import partial
from typing import (
    TYPE_CHECKING,
    Any,
    Iterable,
    Optional,
    Type,
    Union,
)
from urllib.parse import urljoin

from django.conf import settings
from django.contrib.auth.models import User
from django.contrib.contenttypes.fields import (
    GenericForeignKey,
    GenericRelation,
)
from django.contrib.contenttypes.models import ContentType
from django.contrib.postgres.fields import ArrayField
from django.contrib.staticfiles.storage import staticfiles_storage
from django.core.cache import cache
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.core.files.utils import FileProxyMixin
from django.db import (
    models,
    transaction,
)
from django.db.models import (
    Case,
    F,
    Q,
    QuerySet,
    Value,
    When,
)
from django.db.models.functions import Cast
from django.urls import (
    reverse,
    reverse_lazy,
)
from django.utils.functional import cached_property
from django.utils.translation import gettext_lazy as _

from past.utils import old_div
from PIL import Image
from rest_framework.utils.encoders import JSO<PERSON>ncoder
from sorl.thumbnail.fields import ImageField

from custom.enums import (
    Furniture,
    PhysicalProductVersion,
    ShelfType,
    Type13Color,
)
from custom.models import (
    Countries,
    GlobalSettings,
)
from custom.utils.dimensions import Dimensions
from custom.utils.in_memory_cache import expiring_lru_cache
from dynamic_delivery.choices import ShipInRangeChoice
from gallery import data_from_ps
from gallery.constants import (
    ARMREST_HEIGHT,
    FURNITURE_COLOR_CHOICES,
    SEAT_HEIGHT,
)
from gallery.enums import (
    CapeCollectionType,
    ConfiguratorTypeEnum,
    FurnitureCategory,
    FurnitureImageType,
    FurnitureStatusEnum,
    ShelfPatternEnum,
    SottyModuleType,
)
from gallery.ivy_elements import (
    IvyStatisticMixin,
    JettySerialized,
)
from gallery.models.furniture_abstract import (
    FurnitureAbstract,
    JettyAbstract,
    SampleBoxAbstract,
    SellableItemManager,
    SottyAbstract,
    WattyAbstract,
)
from gallery.models.json_functions import (
    JsonbArrayLength,
    JsonbExtractPath,
)
from gallery.services.category_assigner import determine_sofa_category
from gallery.services.delivery_price_calculator import DeliveryPriceCalculator
from gallery.services.sotty_seating_depth_handler import SottySeatingDepthHandler
from gallery.tasks import (
    calculate_weight_task,
    create_sketch_image,
)
from gallery.utils import (
    clean_horizontals,
    compute_adjustment,
    get_openings_load_range,
    get_set_table_for_dna,
    get_total_capacity,
    round_to,
)
from pricing_v3 import calculators
from pricing_v3.assembly_rates import (
    ASSEMBLY_LINEAR_COEFFICIENTS,
    ASSEMBLY_RATES,
)
from pricing_v3.calculators.constants import FACTOR_EURO
from pricing_v3.models import SamplePriceSettings
from pricing_v3.recycle_tax_rates import (
    ECO_TAX_COUNTRIES,
    get_recycle_tax_value,
)
from pricing_v3.registry import get_price_for_datetime
from regions.cached_region import CachedRegionData
from regions.models import (
    Currency,
    Region,
)
from regions.types import RegionLikeObject
from regions.utils import reverse_with_region

if TYPE_CHECKING:
    from carts.models import Cart
    from orders.models import Order

logger = logging.getLogger('cstm')


class AssemblyServicePriceHelpersMixin:
    shelf_type: int

    def assembly_service_based_on_linear_regression(self, shelf_price, region, country):
        min_assembly_price = 140
        country_coefficients = ASSEMBLY_LINEAR_COEFFICIENTS[country]
        shelf_type_coefficients = country_coefficients[self.shelf_type]
        assembly_price = math.ceil(
            shelf_type_coefficients[0] * shelf_price + shelf_type_coefficients[1]
        )
        vat = (region.country.vat + 1) if region.country else 1
        return max(min_assembly_price, assembly_price) * vat

    def assembly_service_based_on_price_buckets(self, shelf_price, country):
        assembly_values = ASSEMBLY_RATES.get(country)
        min_assembly_price = 123
        if not assembly_values:
            assembly_values = ASSEMBLY_RATES.get('default')
            if shelf_price > 1000:
                assembly_price = int(math.ceil(shelf_price * Decimal('0.1492')))
                return max(min_assembly_price, assembly_price)

        assembly_bucket = int(old_div(shelf_price, 50)) - 1 if shelf_price > 100 else 0
        coef_index = assembly_bucket if assembly_bucket < len(assembly_values) else -1
        assembly_price = int(math.ceil(shelf_price * assembly_values[coef_index]))

        return max(min_assembly_price, assembly_price)


@dataclass
class StaticFileFieldPlaceholder(FileProxyMixin):
    path: str

    def __str__(self):
        return self.path

    @property
    def file(self):
        if not getattr(self, '_file'):
            self._file = staticfiles_storage.open(self.path, 'rb')
        return self._file

    @property
    def url(self):
        return staticfiles_storage.url(self.path)

    @property
    def storage(self):
        return staticfiles_storage

    @property
    def name(self):
        return self.path


@dataclass
class CloudinaryFileFieldPlaceholder(FileProxyMixin):
    """This should take only uri for cloudinary asset"""

    path: str
    cloudinary_url = 'https://media.tylko.com/cloudinary/'

    @property
    def url(self):
        return urljoin(self.cloudinary_url, self.path)

    @property
    def name(self):
        return self.path


class FurnitureGridImage(models.Model):
    # todo - probably should be removed
    class Meta(object):
        ordering = ['-id']

    image = ImageField(upload_to='gallery/furniture_grid_image/%Y/%m')
    color = models.IntegerField()
    content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=(
            models.Q(app_label='gallery', model='jetty')
            | models.Q(app_label='gallery', model='watty')
        ),
        on_delete=models.CASCADE,
    )
    object_id = models.PositiveIntegerField()
    furniture = GenericForeignKey('content_type', 'object_id')

    def image_tag(self):
        return '<img src="{}" />'.format(self.image.url)

    image_tag.short_description = 'image'

    def __str__(self):
        return 'FurnitureGridImage[furniture="%s" color=%s image=%s]' % (
            self.object_id,
            self.color,
            self.image,
        )


class FurnitureImageManager(models.Manager):
    def filter_or_create(
        self,
        furniture_id: int,
        content_type: ContentType,
        image_type: FurnitureImageType,
    ) -> tuple['FurnitureImage', bool]:
        """
        There is a race condition for get_or_create method and
        unique_together constraint on FurnitureImage is too expensive to implement
        because of the already existing data.
        """
        furniture_image = self.filter(
            furniture_object_id=furniture_id,
            furniture_content_type=content_type,
            type=image_type,
        ).last()
        if not furniture_image:
            return (
                self.create(
                    furniture_object_id=furniture_id,
                    furniture_content_type=content_type,
                    type=image_type,
                ),
                True,
            )
        return furniture_image, False


class FurnitureImage(models.Model):
    furniture_content_type = models.ForeignKey(
        ContentType,
        limit_choices_to=(
            models.Q(app_label='gallery', model='jetty')
            | models.Q(app_label='gallery', model='sotty')
            | models.Q(app_label='gallery', model='watty')
        ),
        on_delete=models.CASCADE,
    )
    furniture_object_id = models.PositiveIntegerField()
    furniture = GenericForeignKey('furniture_content_type', 'furniture_object_id')
    image = ImageField(upload_to='gallery/furniture_image/%Y/%m', max_length=200)
    image_webp = models.ImageField(
        'Webp version, generate by action',
        upload_to='gallery/furniture_image/%Y/%m',
        blank=True,
        null=True,
        max_length=200,
    )
    type = models.CharField(
        choices=FurnitureImageType.choices,
        default=FurnitureImageType.FEED_IMAGE,
        max_length=100,
    )
    # todo - probably should be removed
    color = models.PositiveIntegerField(
        choices=FURNITURE_COLOR_CHOICES, blank=True, null=True
    )
    enabled = models.BooleanField(default=False)
    position = models.PositiveSmallIntegerField(default=0)

    objects = FurnitureImageManager()

    class Meta(object):
        ordering = ['position']
        indexes = [
            models.Index(
                fields=['furniture_object_id'], name='furniture_object_id_idx'
            ),
        ]

    def __str__(self):
        return 'FurnitureImage[furniture="%s" position=%s image=%s]' % (
            self.furniture,
            self.position,
            self.image,
        )

    @property
    def furniture_type(self):
        return self.furniture_content_type.name

    @property
    def url(self):
        return getattr(self.image, 'url', None)

    @property
    def webp_url(self):
        try:
            webp_url = getattr(self.image_webp, 'url', None)
        except ValueError:
            logging.warning(
                f'Furniture object {self.furniture_object_id} has no webp '
                f'file associated with it.'
            )
            webp_url = self.url
        return webp_url

    def create_webp_version(self):
        if not self.image:
            return

        with Image.open(self.image) as im:
            im_webp = io.BytesIO()
            im.save(im_webp, format='WEBP')
            final_width = 48
            final_height = int(
                (float(im.size[1]) * float((final_width / float(im.size[0]))))
            )
            im.thumbnail((final_width, final_height), Image.ANTIALIAS)
            im_webp.seek(0)
            self.image_webp = InMemoryUploadedFile(
                im_webp,
                None,
                '{}.webp'.format(self.image.name),
                'image/webp',
                im_webp.getbuffer().nbytes,
                None,
            )
            self.save(update_fields=['image_webp'])

    def image_tag(self):
        return '<img src="%s" />' % self.image.url

    image_tag.short_description = 'image'


class Jetty(
    AssemblyServicePriceHelpersMixin,
    IvyStatisticMixin,
    JettyAbstract,
):
    default_title = 'Tylko Shelf'
    product_type = Furniture.jetty.value

    STANDARD_DEPTHS = {
        240,
        320,
        400,
    }

    PF_DESCRIPTION = default_title
    PF_GOOGLE_PRODUCT_CATEGORY = 465
    order_items = GenericRelation('orders.OrderItem', related_query_name='jetty')
    cart_items = GenericRelation('carts.CartItem', related_query_name='jetty')
    grid_images = GenericRelation(
        'gallery.FurnitureGridImage',
        related_query_name='jetty',
    )
    furnitureincategory_set = GenericRelation(
        'rating_tool.FurnitureInCategory',
        related_query_name='jetty',
        object_id_field='furniture_id',
    )

    class Meta:
        # smaller names are being consumed by the FE
        verbose_name = 'jetty'
        verbose_name_plural = 'Jetties'

    def __str__(self):
        return f'{self.product_type}: {self.id}'

    def set_shelf_category(self, furniture_categories):
        if not self.shelf_category:
            furniture_category = furniture_categories.get_category_for_object(self)
            self.shelf_category = furniture_category.name

    def get_pretty_id_name(self):
        return 'ivy'

    def get_project_id(self):
        return '01'

    def get_absolute_url(self):
        object_category_name = self.furniture_category.unidecoded_translated_name_plural
        return reverse_lazy(
            'front-product-shelf',
            kwargs={
                'furniture_category': object_category_name,
                'pk': self.id,
            },
        )

    def get_item_description(self) -> dict[str, str]:
        return {
            'name': _('Ivy Shelf'),
            'material': self.color.translated_material_description,
            'dimensions': 'H%s, W%s, D%s'
            % (
                self.get_height(format=True),
                self.get_width(format=True),
                self.get_depth(format=True),
            ),
        }

    def get_pattern_name(self):
        return ShelfPatternEnum(self.pattern).label

    def get_furniture_type(self):
        return Furniture.jetty.value

    @property
    def furniture_type(self):
        return self.get_furniture_type()

    @property
    def is_desk(self):
        return self.furniture_category == FurnitureCategory.DESK

    def _get_ship_in_range(
        self,
        color=None,
        force_doors=False,
        force_drawers=False,
        force_without_features=False,
        force_sideboard_plus_features=False,
        region=None,
        capacity_strategy=None,
    ) -> ShipInRangeChoice:
        """Returns estimated delivery time in X-X+1 weeks format"""

        color = ShelfType(self.shelf_type).colors(color or self.material)
        features = self.get_item_features(0)
        has_doors = force_doors or 'doors' in features
        has_drawers = force_drawers or 'drawers' in features
        service = self.get_delivery_time_matrix_service(capacity_strategy)
        return service.get_ship_in_range_for_shelf(
            color=color,
            has_doors=has_doors,
            has_drawers=has_drawers,
        )

    def get_delivery_time_matrix_service(self, capacity_strategy=None):
        from dynamic_delivery.services.ship_in_range import (
            JettyShipInRangeMatrixService,
        )

        return JettyShipInRangeMatrixService(
            ShelfType(self.shelf_type),
            capacity_strategy=capacity_strategy,
            is_desk=self.is_desk,
            furniture_category=self.furniture_category,
        )

    def get_dimensions(self):
        """
        Returns furniture dimensions
        :return: :class:`Dimensions` instance
        """
        ds = Dimensions()
        ds.set_dimension(Dimensions.DimensionType.WIDTH, self.get_width())
        ds.set_dimension(Dimensions.DimensionType.HEIGHT, self.get_height())
        ds.set_dimension(Dimensions.DimensionType.DEPTH, self.get_depth())
        return ds

    def get_material_description(self) -> dict[str, str]:
        """
        Returns dict of localised materials including colours
        :return: :class:`dict` {colour{name,html}, material}
        """
        return {
            'colour': {
                'name': self.color.translated_color,
                'html': self.color.hex,
                'html_second_color': self.color.secondary_hex,
            },
            'material': self.get_material(),
        }

    def format_dimensions(self, value, without_cm=False):
        if without_cm:
            return '%d' % int(value)
        return '%dcm' % int(value)

    def get_width(self, format=False, without_cm=False):
        if self.width > 0:
            width = math.ceil(self.width / 10.0)
        elif self.modules:
            width = math.ceil((self.modules[0]['sizex'] + 18) / 10.0)
        else:
            return 0
        if format:
            width = self.format_dimensions(width, without_cm)
        return width

    def get_height(self, format=False, without_cm=False):
        height = self.height / 10
        if format:
            height = self.format_dimensions(height, without_cm)
        return height

    def get_depth(self, format=False, without_cm=False):
        depth = self.depth / 10.0
        if format:
            depth = self.format_dimensions(depth, without_cm)
        return depth

    def get_rows(self):
        used_rows = []
        height = self.get_height() * 10
        summed_height = 0
        for row in self.rows:
            if row is None:
                continue
            if summed_height + row < height:
                used_rows.append(row)
                summed_height += row
        return used_rows

    def get_size(self):
        return '{} cm x {} cm x {} cm'.format(
            self.get_width(), self.get_height(), self.get_depth()
        )

    def get_shelf_price_as_number(
        self,
        region: Optional[RegionLikeObject] = None,
        net=False,
        in_pln=False,
        for_datetime=None,
    ):
        region_name = region.name if region else None
        if for_datetime:
            shelf_price = get_price_for_datetime(for_datetime, region, self)
        else:
            shelf_price = calculators.jetty.calculate_price(
                self, region_name=region_name
            )
        if in_pln:
            shelf_price *= GlobalSettings.factor_euro()
        if net:
            shelf_price /= Decimal(settings.POLISH_VAT_FACTOR)
        return shelf_price

    def get_assembly_price(
        self,
        country: str = None,
        region: Optional[Region] = None,
        for_datetime: Optional[datetime] = None,
    ) -> int:
        """Returns EURO GROSS price for assembly service.

        NOTE: we explicitly add VAT for countries in ASSEMBLY_LINEAR_COEFFICIENTS list.
        Other countries have VAT already included in their coefficients."""
        if not country and not region:
            region = Region.get_other()
        elif country and not region:
            region = Region.objects.filter(name=country).first()
            if region is None:
                region = Region.get_other()

        shelf_price = self.get_shelf_price_as_number(
            region=region, for_datetime=for_datetime
        )

        if country in ASSEMBLY_LINEAR_COEFFICIENTS.keys():
            return self.assembly_service_based_on_linear_regression(
                shelf_price, region, country
            )
        return self.assembly_service_based_on_price_buckets(shelf_price, country)

    def get_delivery_price(self, region=None, price_configuration=None):
        return 0

    def get_estimated_weight_gross(self):
        # NOTE: this was calculated in May 2021, based on get_accurate_weight
        #  so we don't have to ask PS for weight every time.
        #  Has ~93% accuracy.
        front_area = Decimal(self.width) / 1000 * Decimal(self.height) / 1000
        front_m2_weight = Decimal('33.20650214')
        average_drawer_weight = Decimal('9.79765786')
        base_weight = Decimal('7.43513789')
        return (
            front_m2_weight * front_area
            + average_drawer_weight * len(self.drawers)
            + base_weight
        )

    def get_accurate_weight_gross(self):
        serialized_ivy_for_production = data_from_ps.get_serialized_data_from_ps(self)
        serialized_data = JettySerialized(serialized_ivy_for_production)
        return round(serialized_data.packaging_weight, 2)

    @property
    def material_name(self) -> str:
        """Returns color name (eg. MIDNIGHT_BLUE)."""
        return self.color.name

    def get_variant(self, color_prefix=True):
        return (
            '{material}|{furniture_type}|{configurator_type}|{product_version}'.format(
                material=self.translated_material_name
                if color_prefix
                else self.material,
                furniture_type=ShelfType(self.shelf_type).slug,
                configurator_type=self.configurator_type,
                product_version=self.physical_product_version,
            )
        )

    def get_criteo_feed_items(self, feed):
        from product_feeds.services.helpers import ProductFeedJettyHelperCriteo

        records = cache.get('product_feed_criteo_records')
        if not records:
            country = Countries.germany
            region = Region.objects.get(name=country.name)
            currency_rate = Currency.objects.get(
                code=country.currency
            ).current_rate.rate
            records = [
                ProductFeedJettyHelperCriteo(
                    feed_item.furniture_in_category.furniture,
                    country,
                    currency_rate=currency_rate,
                    region=region,
                ).as_dict()
                for feed_item in feed.feed_items
                if isinstance(feed_item.furniture_in_category.furniture, Jetty)
            ]
            cache.set(
                'product_feed_criteo_records',
                json.dumps(records, cls=JSONEncoder),
                None,
            )
            return records

        return json.loads(records)

    def get_max_load(self):
        if self.max_capacity == 0:
            cleaned_horizontals = clean_horizontals(self.horizontals)
            has_doors_or_drawers = bool(self.doors or self.drawers)

            set_table = get_set_table_for_dna(self.shelf_type, self.pattern)
            total = get_total_capacity(
                set_table,
                self.width,
                self.verticals,
                cleaned_horizontals,
                has_doors_or_drawers,
            )
            if self.shelf_type == 1:
                total *= 0.66
            total = round_to(total, 10)

            self.max_capacity = total
            self.save()
        return self.max_capacity

    def get_compartment_max_load(self):
        """Returns max load for one compartment.
        While using you need to remember that it returns bullshit data.
        """
        cleaned_horizontals = clean_horizontals(self.horizontals)
        has_doors_or_drawers = bool(self.doors or self.drawers)
        set_table = get_set_table_for_dna(self.shelf_type, self.pattern)

        adjustment = compute_adjustment(
            set_table,
            has_doors_or_drawers,
            len(cleaned_horizontals) - 1,
        )
        return int(
            get_openings_load_range(
                self.verticals,
                cleaned_horizontals,
                adjustment,
                set_table,
            )[1]
        )

    @property
    def extended(self):
        """Determine if shelf has extended width.

        Extended shelfs are wider than 240cm and contain joints.
        """
        return len(self.joints) > 0 or self.width > 2400 or self.get_width() > 240

    def get_item_features(self, order_type, order=None):
        resp = []
        if len(self.drawers) > 0:
            resp.append('drawers')
        if len(self.doors) > 0:
            resp.append('doors')
        if (
            self.has_plinth
            or len(self.inserts) > 0
            or len(self.cable_management) > 0
            or len(self.long_legs) > 0
        ):
            resp.append('plus_feature')
        if self.extended:
            resp.append('extended')
        if self.has_top_or_bottom_storage:
            resp.append('storage')
            if self.has_bottom_storage:
                resp.append('bottom_storage')
            if self.has_top_storage:
                resp.append('top_storage')
        if self.desk_beams:
            resp.append('desk')
        resp.append('{:.0f}cm'.format(self.depth / 10))
        if order_type != 1 or self.depth not in [240, 320, 400, 500]:
            resp.append('custom')
        if order is not None and order.assembly is True:
            resp.append('assembly')
        return resp

    @property
    def has_plinth(self):
        if isinstance(self.plinth, bool):
            return self.plinth
        else:
            return bool(len(self.plinth))

    @property
    def has_visible_backpanels(self) -> bool:
        """Checks if shelf has visible backpanels (not covered by doors or drawers)."""

        return len(self.backs) > (len(self.doors) + len(self.drawers))

    def get_item_url(self):
        return reverse(
            'front-product-shelf',
            args=(
                self.furniture_category,
                self.id,
            ),
        )

    def get_app_deeplink(self):
        return f'tylkoapp://shelf/{self.id}/'

    @property
    def product_line(self) -> str:
        return ShelfType(self.shelf_type).product_line


class CustomDna(models.Model):
    title = models.CharField(max_length=250)
    author = models.ForeignKey(User, on_delete=models.PROTECT)
    dna = models.FileField(upload_to='gallery/custom_dna/dna/%Y/%m')
    shelf_type = models.IntegerField(
        default=0,
        choices=ShelfType.choices(),
    )
    # Unused in new dnas, but keeps important files for old dnas
    # and needed to reproduce them
    full_file_dna = models.FileField(
        upload_to='gallery/custom_dna/full_dna/%Y/%m',
        null=True,
        blank=True,
    )
    visible_on_web = models.BooleanField(
        default=False, help_text='Make dna visible on web'
    )
    pattern_slot = models.IntegerField(
        help_text='Type1&2 - 0=slant, 1=gradient, 2=pattern, 3=grid, '
        + 'leave empty for configurator+',
        blank=True,
    )
    dna_objects = ArrayField(
        base_field=models.IntegerField(),
        blank=True,
        default=list,
    )
    new_dna_tools = models.BooleanField(
        default=False, help_text='Check if you want to use webdesigner dnatools'
    )
    configurator_type = models.PositiveSmallIntegerField(
        choices=ConfiguratorTypeEnum.choices(),
        default=ConfiguratorTypeEnum.ROW,
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    collection_type = models.CharField(
        max_length=250,
        choices=CapeCollectionType.choices,
        blank=True,
        null=True,
    )

    def __str__(self):
        return (
            f'Custom dna id: {self.id}, '
            f'title: {self.title}, '
            f'added {self.created_at} '
            f'by {self.author}'
        )

    @cached_property
    def dna_json(self) -> dict[str, Any]:
        dna_json = json.load(self.dna)
        # pop unused key from old style dnas.
        dna_json.pop('rhino_only', None)
        return dna_json

    @staticmethod
    def get_solo(shelf_type=0, shelf_pattern=0):
        """Get current active CustomDna object based on shelfType and DnaType"""
        return CustomDna.objects.get(
            visible_on_web=True, shelf_type=shelf_type, pattern_slot=shelf_pattern
        )

    @cached_property
    def physical_product_version(self):
        if self.configurator_type == ConfiguratorTypeEnum.ROW:
            return PhysicalProductVersion.DEFAULT

        if self.collection_type == CapeCollectionType.SIDEBOARD:
            return PhysicalProductVersion.RAPTOR
        return PhysicalProductVersion.DEFAULT

    def get_details(self) -> dict:
        dna_details = {
            'configurator_type': self.configurator_type,
            'json': self.dna_json,
        }
        if self.configurator_type != ConfiguratorTypeEnum.ROW:
            dna_details['available_dnas'] = self.get_available_dnas()
        return dna_details

    def get_available_dnas(self) -> list[dict]:
        """
        Handles patterns ordering: grid, gradient, pattern, frame
        for COLUMN configurators.
        TODO: Remove after configuration API fully supports Sideboards.
        """
        # ordering: grid, gradient, pattern, frame
        available_dnas_ordering = [3, 1, 2, 4, 6, 5]
        object_ids = self.dna_json['superior_object_ids']
        available_dnas = []
        if self.configurator_type == ConfiguratorTypeEnum.SOFA:
            return [
                {
                    'superior_object_id': 50001,
                    'pattern': 1,
                    'order': 1,
                }
            ]

        for object_id in object_ids:
            object_serialization = self.dna_json['serialization']['mesh'][
                str(object_id)
            ]
            object_pattern = object_serialization['constants']['pattern']
            available_dnas.append(
                {
                    'superior_object_id': object_id,
                    'pattern': object_pattern,
                    'order': available_dnas_ordering.index(object_pattern) + 1,
                }
            )
        return sorted(available_dnas, key=lambda k: k['order'])

    def assign_data_from_dna_json(self) -> None:
        collection_type = self.dna_json.get('superior_object_collection', None)
        is_cape_dna = collection_type in CapeCollectionType

        self.collection_type = collection_type
        self.title = self.dna_json.get('dna_name', 'missing title')
        if is_cape_dna and not self.new_dna_tools:
            self._assign_data_from_cape_dna()

    def _assign_data_from_cape_dna(self):
        dna_objects = self.dna_json['superior_object_ids']
        cape_object_type = self.dna_json['superior_object_type']
        if len(dna_objects) == 1:
            dna_object = str(dna_objects[0])
            pattern_slot = self.dna_json['serialization'][cape_object_type][dna_object][
                'constants'
            ].get('pattern', -1)
        else:
            pattern_slot = -1

        self.title = f'{cape_object_type} - {dna_objects}'
        self.dna_objects = dna_objects
        self.pattern_slot = pattern_slot
        self.shelf_type = self.dna_json['superior_object_line']
        self.configurator_type = CapeCollectionType(
            self.collection_type
        ).get_configurator_type(ShelfType(self.shelf_type))

    @cached_property
    def furniture_model(self) -> Type['FurnitureAbstract']:
        """
        Returns appropriate furniture model for CustomDNA.
        """
        if self.configurator_type == ConfiguratorTypeEnum.SOFA:
            return Sotty
        elif (
            self.dna_json['superior_object_collection'] == CapeCollectionType.CHEST
            or self.configurator_type == ConfiguratorTypeEnum.MIXED_ROW_COLUMN
        ):
            return Watty

        return Jetty

    @property
    def furniture_object(self) -> 'FurnitureAbstract':
        """
        Returns appropriate furniture instance for CustomDNA.
        For row configurator type we return preset with id=7645
        and for others we return new unsaved instance with overwritten DNA.
        """
        if self.configurator_type == ConfiguratorTypeEnum.ROW:
            return Jetty.objects.get(id=7645)

        furniture_input_data = {
            'id': 0,
            'furniture_status': FurnitureStatusEnum.SHARED,
            'depth': 320,
            'height': 320,
            'width': 320,
            'shelf_type': self.shelf_type,
            'configurator_type': self.configurator_type,
            'owner_id': self.author.id,
            'physical_product_version': self.physical_product_version,
            'dna_object': self.dna_objects[0],
            'dna_name': self.title,
        }
        if self.furniture_model == Jetty:
            furniture_input_data['pattern'] = self.pattern_slot

        return self.furniture_model(**furniture_input_data)


class SampleBox(SampleBoxAbstract):
    default_title = 'Sample box'
    product_type = Furniture.sample_box.value

    SAMPLE_AREA = Decimal('0.0169')

    SAMPLE_WOODEN_DEPTH = 125
    SAMPLE_WOODEN_WIDTH = 140
    SAMPLE_WOODEN_HEIGHT = 152
    SAMPLE_WOODEN_WEIGHT = 0.9

    SAMPLE_MATERIAL_DEPTH = 5
    SAMPLE_MATERIAL_WIDTH = 150
    SAMPLE_MATERIAL_HEIGHT = 100
    SAMPLE_MATERIAL_WEIGHT = 0.1

    order_items = GenericRelation('orders.OrderItem', related_query_name='sample_box')
    cart_items = GenericRelation('carts.CartItem', related_query_name='sample_box')

    class Meta(object):
        # smaller names are being consumed by the FE
        verbose_name = 'sample box'
        verbose_name_plural = 'Sample boxes'

    def __str__(self):
        return self.default_title

    @property
    def preview(self):
        return CloudinaryFileFieldPlaceholder(
            f'lp/sample/sets/{self.box_variant.variant_type}/A.avif'
        )

    def get_thumbnail(self, geometry_string=None):
        return self.preview

    def get_pretty_id_name(self):
        return 'samplebox'

    def get_furniture_type(self):
        return Furniture.sample_box.value

    @property
    def furniture_category(self):
        return None

    @property
    def furniture_type(self):
        return self.get_furniture_type()

    def get_project_id(self):
        return '00'

    def get_delivery_time_max_week(self, *args, **kwargs):
        return 1

    def get_delivery_time_days(self, *args, **kwargs):
        return 4

    def get_dimensions(self):
        """Returns furniture dimensions
        :return: :class:`Dimensions` instance"""
        ds = Dimensions()
        ds.set_dimension(Dimensions.DimensionType.HEIGHT, self.get_height())
        ds.set_dimension(Dimensions.DimensionType.WIDTH, self.get_width())
        ds.set_dimension(Dimensions.DimensionType.DEPTH, self.get_depth())
        return ds

    def get_weight(self):
        if self.is_material_type:
            return self.SAMPLE_MATERIAL_WEIGHT
        return self.SAMPLE_WOODEN_WEIGHT

    def get_accurate_weight_gross(self):
        if self.is_material_type:
            return self.SAMPLE_MATERIAL_WEIGHT
        return self.SAMPLE_WOODEN_WEIGHT  # with packaging

    def get_item_description(self):
        material = str(self.box_variant.get_translated_name_display())
        dimensions = self.get_dimensions()
        height = dimensions.get_dimension(Dimensions.DimensionType.HEIGHT)
        width = dimensions.get_dimension(Dimensions.DimensionType.WIDTH)

        if self.is_material_type:
            return {
                'name': _('Sample Materials'),
                'material': material,
                'dimensions': f'H{height}cm, W{width}cm ',
            }

        depth = dimensions.get_dimension(Dimensions.DimensionType.DEPTH)
        return {
            'name': _('Sample Set'),
            'material': material,
            'dimensions': f'H{height}cm, W{width}cm, D{depth}cm ',
        }

    @property
    def height(self):
        if self.is_material_type:
            return self.SAMPLE_MATERIAL_HEIGHT
        return self.SAMPLE_WOODEN_HEIGHT

    @property
    def width(self):
        if self.is_material_type:
            return self.SAMPLE_MATERIAL_WIDTH
        return self.SAMPLE_WOODEN_WIDTH

    @property
    def depth(self):
        if self.is_material_type:
            return self.SAMPLE_MATERIAL_DEPTH
        return self.SAMPLE_WOODEN_DEPTH

    def get_height(self, in_mm=False):
        if in_mm is True:
            return self.height
        else:
            return int(math.ceil(self.height / 10.0))

    def get_width(self, in_mm=False):
        if in_mm is True:
            return self.width
        else:
            return int(math.ceil(self.width / 10.0))

    def get_depth(self, in_mm=False):
        if in_mm is True:
            return self.depth
        else:
            return int(math.ceil(self.depth / 10.0))

    def get_delivery_price(self, region=None, price_configuration=None):
        return 0

    def get_shelf_price_as_number(self, region=None, for_datetime=None):
        if self.box_variant and self.box_variant.is_sofa_sample:
            return SamplePriceSettings.get_sofa_sample_price_in_euro()
        return SamplePriceSettings.get_storage_sample_price_in_euro()

    def get_max_load(self):
        return 0

    def get_pattern_name(self):
        variant_type = (
            self.box_variant.get_variant_type_display()
            .replace('TYPE', '')
            .replace(' ', '_')
        )
        return f'SB_{variant_type}'

    def get_pattern_id(self):
        return self.box_variant.variant_type

    def get_size(self):
        return '{} cm x {} cm x {} cm'.format(
            self.get_width(),
            self.get_height(),
            self.get_depth(),
        )

    def get_variant(self):
        return self.get_variant_name()

    def get_variant_name(self):
        return 'SampleBox: {}'.format(self.box_variant.get_variant_type_display())

    def get_item_url(self):
        return reverse('front-compare-shelves')

    def get_recycle_tax_value(self, cart_or_order: Union['Order', 'Cart'] = None):
        return 0

    def get_url_with_region(self, region: 'Region') -> str:
        return reverse_with_region(
            'front-compare-shelves',
            region.get_country().code.lower(),
        )


class WattyManager(SellableItemManager):
    def get_with_assembly_service_required(
        self, ids: Iterable[int]
    ) -> QuerySet['Watty']:
        return self.filter(
            id__in=ids,
            shelf_type=ShelfType.TYPE03,
            shelf_category=FurnitureCategory.WARDROBE,
        )


class Watty(
    AssemblyServicePriceHelpersMixin,
    WattyAbstract,
):
    product_type = Furniture.watty.value
    order_items = GenericRelation('orders.OrderItem', related_query_name='watty')
    cart_items = GenericRelation('carts.CartItem', related_query_name='watty')
    grid_images = GenericRelation(
        'gallery.FurnitureGridImage', related_query_name='watty'
    )
    furnitureincategory_set = GenericRelation(
        'rating_tool.FurnitureInCategory',
        related_query_name='watty',
        object_id_field='furniture_id',
    )

    objects = WattyManager()

    STANDARD_DEPTHS = {
        630,
        530,
        430,
    }

    class Meta:
        # smaller names are being consumed by the FE
        verbose_name = 'watty'
        verbose_name_plural = 'Watties'

    @property
    def default_title(self):
        return (
            'Tylko Wardrobe'
            if self.furniture_category == FurnitureCategory.WARDROBE
            else 'Tylko Shelf'
        )

    @property
    def assembly_service(self):
        if self.is_t03_wardrobe:
            return True
        return super().assembly_service

    def get_assembly_price(
        self,
        country: str = None,
        region: Optional[Region] = None,
        for_datetime: Optional[datetime] = None,
    ) -> int:
        """Returns EURO GROSS price for assembly service for Type13 only!

        NOTE: Type03 AS is already part of pricing algorithm, and does not need to be
        calculated as separate service.

        NOTE: we explicitly add VAT for countries in ASSEMBLY_LINEAR_COEFFICIENTS list.
        Other countries have VAT already included in their coefficients."""

        if self.is_t03_wardrobe:
            return 0

        if not country and not region:
            region = Region.get_other()
        elif country and not region:
            region = Region.objects.filter(name=country).first()

        wardrobe_price = self.get_shelf_price_as_number(
            region=region, for_datetime=for_datetime
        )

        if country in ASSEMBLY_LINEAR_COEFFICIENTS.keys():
            return self.assembly_service_based_on_linear_regression(
                wardrobe_price,
                region,
                country,
            )
        return self.assembly_service_based_on_price_buckets(wardrobe_price, country)

    def get_pretty_id_name(self):
        return 'wardrobe'

    def get_furniture_type(self):
        return Furniture.watty.value

    @property
    def furniture_type(self):
        return self.get_furniture_type()

    def get_project_id(self):
        return '07'

    def get_dimensions(self):
        """Returns furniture dimensions.

        Returns:
            :class:`Dimensions` instance
        """
        ds = Dimensions()
        ds.set_dimension(Dimensions.DimensionType.HEIGHT, self.get_height())
        ds.set_dimension(Dimensions.DimensionType.WIDTH, self.get_width())
        ds.set_dimension(Dimensions.DimensionType.DEPTH, self.get_depth())
        return ds

    def get_accurate_weight_gross(self):
        return self.get_weight() * 1.3  # with packaging

    def get_item_description(self) -> dict[str, str]:
        return {
            'name': (
                _('Wardrobe')
                if self.furniture_category == FurnitureCategory.WARDROBE
                else _('Ivy Shelf')
            ),
            'material': self.color.translated_material_description,
            'dimensions': 'H%scm, W%scm, D%scm'
            % (
                self.get_dimensions().get_dimension(Dimensions.DimensionType.HEIGHT),
                self.get_dimensions().get_dimension(Dimensions.DimensionType.WIDTH),
                self.get_dimensions().get_dimension(Dimensions.DimensionType.DEPTH),
            ),
        }

    def get_height(self, in_mm=False):
        if in_mm is True:
            return self.height
        return math.ceil(self.height / 10.0)

    def get_width(self, in_mm=False):
        if in_mm is True:
            return self.width
        return math.ceil(self.width / 10.0)

    def get_depth(self, in_mm=False):
        if in_mm is True:
            return self.depth
        return math.ceil(self.depth / 10.0)

    def get_delivery_price(self, region=None, price_configuration=None):
        return 0

    def get_shelf_price_as_number(
        self,
        region: Optional[RegionLikeObject] = None,
        in_pln: bool = False,
        for_datetime: Optional[datetime] = None,
    ):
        region_name = region.name if region else None
        if for_datetime:
            price = get_price_for_datetime(for_datetime, region, self)
        else:

            if self.shelf_type == ShelfType.TYPE13:
                price = calculators.t13.calculate_price(self, region_name=region_name)
            elif self.shelf_type == ShelfType.VENEER_TYPE13:
                price = calculators.t13_veneer.calculate_price(
                    self, region_name=region_name
                )
            elif self.shelf_type == ShelfType.TYPE03:
                price = calculators.t03.calculate_price(self, region_name=region_name)
            elif self.shelf_type == ShelfType.TYPE23:
                price = calculators.t23.calculate_price(self, region_name=region_name)
            elif self.shelf_type == ShelfType.TYPE24:
                price = calculators.t24.calculate_price(self, region_name=region_name)
            elif self.shelf_type == ShelfType.TYPE25:
                price = calculators.t25.calculate_price(self, region_name=region_name)

        if in_pln:
            price *= FACTOR_EURO
        return price

    def get_max_load(self):
        """
        For low expressions, we can't use 250 as a max load.

        I mean for other types we shouldn't as well, but nobody complained yet.
        For T24 (hanging) the max load is well-defined -
            10kg per slab <60cm, 15kg otherwise.
        For T23 and T25 let's just multiply it by 2, because why not.
        Also, in T24 we could just count the slabs in the lowest layer,
          but in T23 and T25 the lowest layer has only one slab, so we have to
          rely on the distance between walls.
        """
        if self.shelf_type in {
            ShelfType.TYPE03,
            ShelfType.TYPE13,
            ShelfType.VENEER_TYPE13,
        }:
            return 250
        widths = [
            abs(wall2['x1'] - wall1['x2'])
            for wall1, wall2 in zip(self.walls, self.walls[1::])
        ]
        max_load = sum(
            15 if compartment_width > 600 else 10 for compartment_width in widths
        )
        no_of_levels = len({slab['y1'] for slab in self.slabs})
        multiplier = 1 if self.shelf_type == ShelfType.TYPE24 else 2
        return max_load * no_of_levels * multiplier

    @property
    def has_mixed_height(self) -> bool:
        if self.shelf_type not in [ShelfType.TYPE13, ShelfType.VENEER_TYPE13]:
            return False
        return (
            len({component.get('local_height', None) for component in self.components})
            > 1
        )

    @property
    def has_mixed_depth(self) -> bool:
        if self.shelf_type not in [ShelfType.TYPE13, ShelfType.VENEER_TYPE13]:
            return False
        return (
            len({component.get('local_depth', None) for component in self.components})
            > 1
        )

    def get_pattern_name(self):
        return 'simple'

    def get_style_name(self):
        return {
            ShelfType.TYPE23: 'minimal',
            ShelfType.TYPE24: 'floating',
            ShelfType.TYPE25: 'bold',
        }.get(self.shelf_type, 'simple')

    def get_size(self):
        return '{} cm x {} cm x {} cm'.format(
            self.get_width(), self.get_height(), self.get_depth()
        )

    def get_item_url(self):
        return reverse(
            'front-product-watty',
            args=(
                self.furniture_category,
                self.id,
            ),
        )

    def get_material(self):
        if self.is_t03_wardrobe:
            return super().get_material()
        if self.shelf_type == ShelfType.VENEER_TYPE13:
            return _('veneer_t13')  # TODO: change to actual translation key
        if Type13Color(self.color).is_plywood_color:
            return _('particleboard_with_plywood_t13')
        return _('particleboard_t13')

    @property
    def material_name(self) -> str:
        """Returns color name (eg. BEIGE_PINK)."""
        return self.color.name

    def get_area(self):
        """Front area [mm^2]."""
        return self.width * self.height

    def is_overhead(self):
        return self.height > 2000

    def has_height_topology(self):
        wall_height_by_x = defaultdict(int)
        for wall in self.walls:
            wall_height_by_x[wall['x1']] = max(wall_height_by_x[wall['x1']], wall['y2'])
        return len(set(wall_height_by_x.values())) > 1

    def has_depth_topology(self):
        wall_depths = {wall['z2'] for wall in self.walls}
        return len(wall_depths) > 1

    def get_item_features(self, order_type, order=None):
        resp = []
        if len(self.doors) > 0:
            resp.append('doors')
        if len(self.drawers) > 0:
            resp.append('drawers')
        if self.is_overhead():
            resp.append('overhead')
        if len(self.lighting) > 0:
            resp.append('lighting')
        if self.has_height_topology():
            resp.append('height_topology')
        if self.has_depth_topology():
            resp.append('depth_topology')
        if self.shelf_type == ShelfType.TYPE03 or (
            order is not None and order.assembly is True
        ):
            resp.append('assembly')
        return resp

    def get_absolute_url(self):
        return reverse_lazy(
            'front-product-watty',
            args=(self.furniture_category, self.id),
        )

    def get_material_description(self) -> dict[str, str]:
        """
        Returns dict of localised materials including colours
        :return: :class:`dict` {colour{name,html}, material}
        """
        return {
            'colour': {
                'name': self.color.translated_color,
                'html': self.color.hex,
                'html_second_color': self.color.secondary_hex,
            },
            'material': self.get_material(),
        }

    def get_material_name(self, get_for_batch=False) -> str:
        """Get a verbose material name with board code."""
        # This method for Jetty is in IvyMaterialMixin
        return self.color.get_material_name()

    def get_app_deeplink(self):
        return f'tylkoapp://wardrobe/{self.id}/'

    def get_variant(self):
        return (
            '{material}|{furniture_type}|{configurator_type}|{product_version}'.format(
                material=self.material,
                furniture_type=ShelfType(self.shelf_type).slug,
                configurator_type=self.configurator_type,
                product_version=self.physical_product_version,
            )
        )

    def _get_ship_in_range(
        self,
        color=None,
        region=None,
        capacity_strategy=None,
        *args,
        **kwargs,
    ):
        color = ShelfType(self.shelf_type).colors(color or self.material)
        matrix_service = self.get_delivery_time_matrix_service(capacity_strategy)
        return matrix_service.get_ship_in_range_for_shelf(color=color)

    def get_delivery_time_matrix_service(self, capacity_strategy=None):
        from dynamic_delivery.services.ship_in_range import (
            WattyShipInRangeMatrixService,
        )

        return WattyShipInRangeMatrixService(
            ShelfType(self.shelf_type),
            capacity_strategy=capacity_strategy,
            furniture_category=self.furniture_category,
        )

    @property
    def has_external_drawers(self) -> bool:
        return self.has_drawers_subtype('e')

    @property
    def has_internal_drawers(self) -> bool:
        # Type03 internal drawers have subtype 'i'
        # Type13 internal drawers that are behind doors have subtype 'd' and open
        # drawers that are still internal (without handles) have subtype 'i'
        return self.has_drawers_subtype('i') or self.has_drawers_subtype('d')

    def has_drawers_subtype(self, subtype: str) -> bool:
        return any(drawer.get('subtype', '') == subtype for drawer in self.drawers)

    @property
    def has_double_hangs(self) -> bool:
        """Informs if a wardrobe has two hangs placed one under another"""
        if not self.bars:
            return False
        bars_x = [(bar['x1'], bar['x2']) for bar in self.bars]
        return len(bars_x) != len(set(bars_x))

    @property
    def has_front_facing_rail(self) -> bool:
        return any(bar.get('subtype', '') == 'z' for bar in self.bars)

    @property
    def product_line(self) -> Optional[str]:
        return ShelfType(self.shelf_type).product_line


class SottyManager(SellableItemManager):
    @expiring_lru_cache(ttl=settings.SOTTY_SINGLE_MODULES_CACHE_TTL_SECONDS)
    def get_single_modules_presets(
        self, covers_only: bool, module_type: SottyModuleType | None = None
    ) -> Iterable[dict]:
        """
        Cover presets were generated to be used as SKU's
        Do not filter it again, cause the result is cached
        """
        single_module_filtering = Q(
            layout_len=1, modules_len=1, covers_only=covers_only
        )
        module_type_filtering = Q(module_type=module_type) if module_type else Q()
        conditional_expressions = [
            # assumed that only single module filtered
            When(
                configurator_params__layout__0__modules__0__type=module_type,
                then=Value(module_type),
            )
            for module_type in SottyModuleType.values
        ]
        return (
            self.get_queryset()
            .filter(preset=True)
            .annotate(
                layout_len=JsonbArrayLength(F('configurator_params__layout')),
                modules_len=JsonbArrayLength(
                    JsonbExtractPath(
                        F('configurator_params'),
                        Value('layout'),
                        Value('0'),
                        Value('modules'),
                    )
                ),
            )
            .filter(single_module_filtering)
            .annotate(
                module_type=Case(
                    *conditional_expressions,
                    default=None,
                    output_field=models.CharField(),
                ),
                material=F('materials__0'),
            )
            .filter(module_type_filtering)
            .annotate(
                alt_depth=Case(
                    When(
                        module_type=SottyModuleType.EXTENDED_CHAISE_LONGUE.value,
                        then=Cast(F('seaters__0__depth'), models.IntegerField()),
                    ),
                    default=F('depth'),
                ),
                extra_depth=Case(
                    When(
                        module_type=SottyModuleType.EXTENDED_CHAISE_LONGUE.value,
                        # yes, it's a width
                        then=F('footrests__0__width'),
                    ),
                    default=None,
                ),
            )
            .values(
                'id',
                'material',
                'width',
                'alt_depth',
                'extra_depth',
                'module_type',
            )
            .annotate(depth=F('alt_depth'))
            .values(
                'id',
                'material',
                'width',
                'depth',
                'extra_depth',
                'module_type',
            )
        )


class Sotty(SottyAbstract):
    default_title = 'Tylko Sofa'
    furniture_type = Furniture.sotty.value
    product_type = Furniture.sotty.value

    order_items = GenericRelation('orders.OrderItem', related_query_name='sotty')
    cart_items = GenericRelation('carts.CartItem', related_query_name='sotty')

    objects = SottyManager()

    class Meta:
        # smaller names are being consumed by the FE
        verbose_name = 'sotty'
        verbose_name_plural = 'Sotties'

    def save(self, *args, **kwargs):
        has_geometry_field_changed = 'update_fields' in kwargs and any(
            geometry_field in kwargs.get('update_fields', [])
            for geometry_field in self.GEOMETRY_FIELDS
        )
        is_new = self.pk is None
        if is_new or has_geometry_field_changed:
            self.shelf_category = determine_sofa_category(sotty=self)
        super().save(*args, **kwargs)
        if is_new:
            binded_task = partial(
                create_sketch_image.delay,
                furniture_type=self.furniture_type,
                furniture_id=self.id,
            )
            transaction.on_commit(binded_task)
        elif is_new or has_geometry_field_changed:
            binded_task = partial(
                create_sketch_image.delay,
                furniture_type=self.furniture_type,
                furniture_id=self.id,
            )
            binded_calculate_weight_task = partial(
                calculate_weight_task.delay,
                furniture_type=self.furniture_type,
                furniture_id=self.id,
            )
            transaction.on_commit(binded_task)
            transaction.on_commit(binded_calculate_weight_task)

    def get_furniture_type(self):
        return self.furniture_type

    # FIXME: add to fix problem with communication with logisitc
    def get_pattern_name(self):
        return 'sotty'

    def get_shelf_price_as_number(
        self,
        region: RegionLikeObject | None = None,
        for_datetime: datetime | None = None,
    ):
        region_name = region.name if region else None
        if for_datetime:
            return get_price_for_datetime(for_datetime, region, self)
        return calculators.s01.calculate_price(self, region_name)

    def get_assembly_price(
        self,
        country: str = None,
        region: Region | None = None,
        for_datetime: datetime | None = None,
    ) -> int:
        """
        Assembly for Sotty doesn't exist.
        There is a White Gloves Delivery Service
        """
        return 0

    def get_accurate_weight_gross(self) -> float:
        serialized_ivy_for_production = data_from_ps.get_serialized_data_from_ps(self)
        serialized_data = JettySerialized(serialized_ivy_for_production)
        return round(serialized_data.packaging_weight, 2)

    def get_estimated_weight_gross(self) -> float:
        return self.weight if self.weight else 0

    def get_item_features(self, order_type, order=None):
        features = []
        if len(self.chaise_longues) > 0:
            features.append('chaise_longue')
        if len(self.corners) > 0:
            features.append('corner')
        if len(self.footrests) > 0:
            features.append('footrest')
        for depth in ShelfType.SOFA_TYPE01.standard_depths:
            if any(module['depth'] == depth for module in self.corners + self.seaters):
                features.append(f'depth_{depth}')
        return features

    def get_delivery_time_matrix_service(self, capacity_strategy=None):
        from dynamic_delivery.services.ship_in_range import (
            SottyShipInRangeMatrixService,
        )

        return SottyShipInRangeMatrixService(
            ShelfType(self.shelf_type),
            capacity_strategy=capacity_strategy,
        )

    def _get_ship_in_range(
        self,
        color=None,
        region=None,
        capacity_strategy=None,
        *args,
        **kwargs,
    ):
        matrix_service = self.get_delivery_time_matrix_service(capacity_strategy)
        if color:
            color = self.shelf_type.colors(color)
            return matrix_service.get_ship_in_range_for_shelf(color=color)
        colors = [self.shelf_type.colors(material) for material in self.materials]
        return matrix_service.get_ship_in_range_for_many_colors(colors=colors)

    def get_dimensions(self):
        """Returns furniture dimensions.

        Returns:
            :class:`Dimensions` instance
        """
        ds = Dimensions()
        ds.set_dimension(Dimensions.DimensionType.HEIGHT, self.get_height())
        ds.set_dimension(Dimensions.DimensionType.WIDTH, self.get_width())
        ds.set_dimension(Dimensions.DimensionType.DEPTH, self.get_depth())
        return ds

    def get_item_description(self) -> dict[str, str]:
        return {
            'name': FurnitureCategory(self.shelf_category).translated_name,
            'material': self.color.translated_material_description,
            'dimensions': 'H%scm, W%scm, D%scm'
            % (
                self.get_dimensions().get_dimension(Dimensions.DimensionType.HEIGHT),
                self.get_dimensions().get_dimension(Dimensions.DimensionType.WIDTH),
                self.get_dimensions().get_dimension(Dimensions.DimensionType.DEPTH),
            ),
        }

    # TODO: QK-677
    def get_height(self, in_mm=False):
        if in_mm is True:
            return self.height
        else:
            return int(math.ceil(self.height / 10.0))

    def get_width(self, in_mm=False):
        if in_mm is True:
            return self.width
        else:
            return int(math.ceil(self.width / 10.0))

    def get_depth(self, in_mm=False):
        if in_mm is True:
            return self.depth
        else:
            return int(math.ceil(self.depth / 10.0))

    def get_seating_depth(self) -> int | None:
        """return value in centimeters (cm)"""
        handler = SottySeatingDepthHandler(self)
        return handler.get_seating_depth()

    def get_seating_width(self) -> int:
        """only for Sotty with seaters without corners and chaise_longues"""
        if self.corners and self.chaise_longues:
            raise NotImplementedError(
                'Seater width for corner or chaise longue sofas not known'
            )
        return sum(seater['width'] for seater in self.seaters)

    def get_armrest_height(self) -> int:
        """Since this value never changes, return a constant value in (cm)."""
        return ARMREST_HEIGHT

    def get_seat_height(self) -> int:
        """Since this value never changes, return a constant value in (cm)."""
        return SEAT_HEIGHT

    def get_eco_tax(self, region: Region) -> Decimal | None:
        if region.name not in ECO_TAX_COUNTRIES:
            return None
        return get_recycle_tax_value(self.weight) if self.weight else None

    def get_size(self):
        return '{} cm x {} cm x {} cm'.format(
            self.get_width(), self.get_height(), self.get_depth()
        )

    def get_max_load(self):
        # FIXME(SOTTY)
        # very dummy calculations
        if self.covers_only:
            return 2
        return (
            len(self.armrests) * 1
            + len(self.chaise_longues) * 20
            + len(self.corners) * 15
            + len(self.footrests) * 5
            + len(self.seaters) * 10
        )

    def get_item_url(self):
        return reverse(
            'front-product-sotty',
            args=(self.furniture_category, self.id),
        )

    # Because Sotties have their own PDP view,
    # we need to generate a specific URL for it.
    def get_item_url_with_region(self, region: RegionLikeObject) -> str:
        if isinstance(region, CachedRegionData):
            country_code = region.country_code.lower()
        else:
            country_code = region.country.code.lower()
        pdp_category_name = self.furniture_category.translated_name_pdp
        return reverse_with_region(
            'front-product-sotty',
            region_code=country_code,
            args=(pdp_category_name, self.id),
        )

    def get_absolute_url(self):
        return reverse_lazy(
            'front-product-sotty-configurator',
            args=(self.id,),
        )

    def get_variant(self):
        return (
            '{material}|{furniture_type}|{configurator_type}|{product_version}'.format(
                material=self.material,
                furniture_type=ShelfType(self.shelf_type).slug,
                configurator_type=self.configurator_type,
                product_version=self.physical_product_version,
            )
        )

    def get_material_description(self) -> dict[str, str]:
        return {
            'colour': {
                'name': self.color.translated_color,
                'html': self.color.hex,
                'html_second_color': self.color.secondary_hex,
            },
            'material': self.get_material(),
        }

    def get_delivery_price(self, region=None) -> Decimal:
        calculator = DeliveryPriceCalculator(furniture=self, region=region)
        return calculator.calculate()

    # TODO remove this method after cleaning in feeds serializer
    #  app_deeplink was intended for the mobile app, which we no longer have.
    def get_app_deeplink(self):
        return f'tylkoapp://sotty/{self.id}/'

    @property
    def modules_number(self) -> int:
        return sum(
            [
                len(self.footrests),
                len(self.corners),
                len(self.seaters),
                len(self.chaise_longues),
            ]
        )

    @property
    def product_line(self) -> str:
        return ShelfType(self.shelf_type).product_line


class SottyProxyForPresetAnalysis(Sotty):
    class Meta:
        proxy = True
