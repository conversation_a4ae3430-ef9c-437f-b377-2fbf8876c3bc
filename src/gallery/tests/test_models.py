from decimal import Decimal
from unittest import mock
from unittest.mock import (
    PropertyMock,
    patch,
)

from django.utils import translation

import pytest

from custom import enums
from custom.enums import (
    LanguageEnum,
    ShelfType,
    Type01Color,
    Type02Color,
    Type03Color,
    VeneerType01Color,
)
from custom.enums.colors import Sofa01Color
from dynamic_delivery.choices import ShipInRangeChoice
from dynamic_delivery.services.ship_in_range import SottyShipInRangeMatrixService
from gallery.enums import (
    FurnitureCategory,
    FurnitureImageType,
)
from gallery.exceptions import GenericForeignKeyProtectedException
from gallery.enums import SottyModuleType
from gallery.models import (
    FurnitureImage,
    Jetty,
    Sotty,
    Watty,
)


@pytest.fixture(scope='function')
def bookcase_jetty(jetty_factory):
    return jetty_factory(shelf_category=FurnitureCategory.BOOKCASE)


@pytest.fixture(scope='function')
def sofa_sotty(sotty_factory):
    return sotty_factory(shelf_category=FurnitureCategory.TWO_SEATER)


@pytest.fixture(scope='function')
def wardrobe_watty(watty_factory):
    return watty_factory(shelf_category=FurnitureCategory.WARDROBE)


@pytest.mark.skip('black friday changes - hardcoded values')
@pytest.mark.django_db
class TestJettyDeliveryTime:
    @pytest.mark.parametrize(
        ('force_doors', 'force_drawers', 'force_without_features', 'expected_time'),
        [
            (False, False, False, 2),
            (False, False, True, 2),
            (True, False, True, 6),
            (False, True, True, 6),
            (True, True, True, 6),
        ],
    )
    def test_get_delivery_time_when_dynamic_delivery_times_not_in_cache_for_type01(
        self,
        force_doors,
        force_drawers,
        force_without_features,
        expected_time,
        jetty_factory,
    ):
        with mock.patch('gallery.models.models.cache.get', return_value=None):
            jetty = jetty_factory(
                shelf_type=enums.ShelfType.TYPE01.value,
                doors=[],
                drawers=[],
            )
            delivery_time_weeks = jetty.get_delivery_time_max_week(
                force_doors=force_doors,
                force_drawers=force_drawers,
                force_without_features=force_without_features,
            )
            assert delivery_time_weeks == expected_time

    @pytest.mark.parametrize(
        ('force_doors', 'force_drawers', 'force_without_features', 'expected_time'),
        [
            (False, False, False, 5),
            (False, False, True, 5),
            (True, False, True, 5),
            (False, True, True, 6),
            (True, True, True, 6),
        ],
    )
    def test_get_delivery_time_when_dynamic_delivery_times_not_in_cache_for_type02(
        self,
        force_doors,
        force_drawers,
        force_without_features,
        expected_time,
        jetty_factory,
    ):
        with mock.patch('gallery.models.models.cache.get', return_value=None):
            jetty = jetty_factory(
                shelf_type=enums.ShelfType.TYPE02.value,
                doors=[],
                drawers=[],
            )
            delivery_time_weeks = jetty.get_delivery_time_max_week(
                force_doors=force_doors,
                force_drawers=force_drawers,
                force_without_features=force_without_features,
            )
            assert delivery_time_weeks == expected_time

    @pytest.mark.parametrize(
        (
            'color',
            'force_doors',
            'force_drawers',
            'force_without_features',
            'expected_time',
        ),
        [
            # white
            (0, False, False, False, 11),
            (0, False, False, True, 11),
            (0, True, False, True, 21),
            (0, False, True, True, 31),
            (0, True, True, True, 31),
            # black
            (1, False, False, False, 41),
            (1, False, False, True, 41),
            (1, True, False, True, 51),
            (1, False, True, True, 61),
            (1, True, True, True, 61),
            # grey
            (3, False, False, False, 71),
            (3, False, False, True, 71),
            (3, True, False, True, 81),
            (3, False, True, True, 91),
            (3, True, True, True, 91),
            # aubergine
            (4, False, False, False, 101),
            (4, False, False, True, 101),
            (4, True, False, True, 111),
            (4, False, True, True, 121),
            (4, True, True, True, 121),
            # natural
            (5, False, False, False, 133),
            (5, False, False, True, 133),
            (5, True, False, True, 23),
            (5, False, True, True, 143),
            (5, True, True, True, 143),
            # legacy color
            (2, False, False, False, 6),
            (2, False, False, True, 6),
            (2, True, False, True, 6),
            (2, False, True, True, 6),
            (2, True, True, True, 6),
        ],
    )
    def test_get_delivery_time_for_type01(
        self,
        color,
        force_doors,
        force_drawers,
        force_without_features,
        expected_time,
        jetty_factory,
    ):
        cached_data = {
            enums.ShelfType.TYPE01.value: {
                'color_0': 10,
                'color_0_with_doors': 20,
                'color_0_with_drawers': 30,
                'color_1': 40,
                'color_1_with_doors': 50,
                'color_1_with_drawers': 60,
                'color_3': 70,
                'color_3_with_doors': 80,
                'color_3_with_drawers': 90,
                'color_4': 100,
                'color_4_with_doors': 110,
                'color_4_with_drawers': 120,
                'color_5': 130,
                'color_5_with_drawers': 140,
            },
        }
        cached_data[enums.ShelfType.TYPE02.value] = {
            key: 0 for key in cached_data[enums.ShelfType.TYPE01.value]
        }
        with mock.patch('gallery.models.models.cache.get', return_value=cached_data):
            jetty = jetty_factory(
                shelf_type=enums.ShelfType.TYPE01.value, doors=[], drawers=[]
            )
            delivery_time_weeks = jetty.get_delivery_time_max_week(
                color=color,
                force_doors=force_doors,
                force_drawers=force_drawers,
                force_without_features=force_without_features,
            )
            assert delivery_time_weeks == expected_time

    def test_get_delivery_time_when_old_dynamic_delivery_data_in_cache(
        self,
        jetty_factory,
    ):
        cached_data = {
            'color_0': 10,
            'color_0_with_doors': 20,
            'color_0_with_drawers': 30,
            'color_1': 40,
            'color_1_with_doors': 50,
            'color_1_with_drawers': 60,
            'color_3': 70,
            'color_3_with_doors': 80,
            'color_3_with_drawers': 90,
            'color_4': 100,
            'color_4_with_doors': 110,
            'color_4_with_drawers': 120,
            'color_5': 130,
            'color_5_with_drawers': 140,
        }
        with mock.patch('gallery.models.models.cache.get', return_value=cached_data):
            jetty = jetty_factory(
                shelf_type=enums.ShelfType.TYPE02.value,
                doors=[],
                drawers=[],
            )
            delivery_time_weeks = jetty.get_delivery_time_max_week(
                color=Type01Color.WHITE.value,
            )
            assert delivery_time_weeks == 6


@pytest.mark.django_db
class TestJetty:
    @pytest.mark.parametrize(
        ('shelf_price', 'country', 'expected_assembly_price', 'vat'),
        [
            (1000, 'switzerland', Decimal('141'), 0),
            (1000, 'france', Decimal('201'), 0.2),
            (50, 'other', Decimal('123'), 0.2),  # min price
            (500, 'other', Decimal('123'), 0.2),
            (5000, 'other', Decimal('746'), 0.2),
            (1000, 'united_kingdom', Decimal('170.8'), 0.22),  # min price
            (5000, 'united_kingdom', Decimal('456.28'), 0.22),
            (10000, 'united_kingdom', Decimal('891.82'), 0.22),
            (1000, 'denmark', Decimal('175'), 0.25),  # min price
            (5000, 'denmark', Decimal('336.25'), 0.25),
            (10000, 'denmark', Decimal('660'), 0.25),
        ],
    )
    def test_get_assembly_price(
        self,
        mocker,
        jetty,
        shelf_price: int,
        country: str,
        expected_assembly_price: int,
        vat: float,
        region_factory,
        country_factory,
    ):
        region = region_factory(name=country)
        country_factory(name=country, region=region, vat=vat)
        mocker.patch(
            'gallery.models.models.Jetty.get_shelf_price_as_number',
            return_value=shelf_price,
        )
        assembly_price = jetty.get_assembly_price(country=country)

        assert assembly_price == expected_assembly_price

    # COLOR & MATERIALS

    @pytest.mark.parametrize(
        ('shelf_type', 'material'),
        [
            (ShelfType.TYPE01, Type01Color.AUBERGINE),
            (ShelfType.TYPE02, Type02Color.SKY_BLUE),
            (ShelfType.VENEER_TYPE01, VeneerType01Color.OAK),
        ],
    )
    def test_color_property(
        self,
        jetty_factory,
        shelf_type: int,
        material: int,
    ):
        jetty = jetty_factory(shelf_type=shelf_type, material=material)
        assert jetty.color == material

    def test_material_name_property(self, jetty_factory):
        jetty = jetty_factory(
            shelf_type=ShelfType.TYPE02, material=Type02Color.MIDNIGHT_BLUE
        )
        assert jetty.material_name == 'MIDNIGHT_BLUE'

    @pytest.mark.parametrize(
        ('shelf_type', 'expected_material'),
        [
            (ShelfType.TYPE01, 'Plywood'),
            (ShelfType.TYPE02, ''),
            (ShelfType.VENEER_TYPE01, 'Particle board + veneer'),
        ],
    )
    def test_get_material(
        self,
        jetty_factory,
        shelf_type: int,
        expected_material: str,
    ):
        with translation.override(LanguageEnum.EN):
            jetty = jetty_factory(shelf_type=shelf_type)
            assert jetty.get_material() == expected_material

    @pytest.mark.parametrize(
        ('shelf_type', 'material', 'expected'),
        [
            (ShelfType.TYPE01, Type01Color.WHITE, 'White Plywood'),
            (ShelfType.TYPE01, Type01Color.DUSTY_PINK, 'Dusty Pink Plywood'),
            (ShelfType.TYPE02, Type02Color.SAND, 'Sand + Midnight Blue'),
            (ShelfType.TYPE02, Type02Color.SKY_BLUE, 'Sky Blue'),
            (ShelfType.VENEER_TYPE01, VeneerType01Color.OAK, 'Oak Veneer'),
        ],
    )
    def test_get_item_desc(
        self,
        jetty_factory,
        shelf_type: int,
        material: int,
        expected: str,
    ):
        with translation.override(LanguageEnum.EN):
            jetty = jetty_factory(shelf_type=shelf_type, material=material)
            assert jetty.get_item_description()['material'] == expected

    def test_raises_error_when_trying_to_delete_jetty_used_on_plp(
        self,
        jetty,
        catalogue_entry_factory,
    ):
        catalogue_entry_factory(furniture=jetty)
        with pytest.raises(GenericForeignKeyProtectedException):
            jetty.delete()

    def test_raises_error_when_trying_to_delete_jetties_used_on_plp(
        self,
        jetty_factory,
        catalogue_entry_factory,
    ):
        jetty_1 = jetty_factory()
        jetty_2 = jetty_factory()
        catalogue_entry_factory(furniture=jetty_1)
        with pytest.raises(GenericForeignKeyProtectedException):
            Jetty.objects.filter(id__in={jetty_1.id, jetty_2.id}).delete()

    @pytest.mark.parametrize(
        ('lang', 'expected_url'),
        [
            ('da', '/da/mobel/bogreoler/'),
            ('de', '/de/mobel/bucherregale/'),
            ('en', '/en/furniture/bookcases/'),
            ('es', '/es/mueble/librerias/'),
            ('fr', '/fr/meuble/bibliotheques/'),
            ('it', '/it/mobili/librerie/'),
            ('nl', '/nl/meubel/boekenkasten/'),
            ('no', '/no/mobler/bokhyller/'),
            ('pl', '/pl/furniture/regaly/'),
            ('sv', '/sv/mobler/bokhyllor/'),
        ],
    )
    def test_get_absolute_url(self, bookcase_jetty, lang, expected_url):
        with translation.override(lang):
            url = bookcase_jetty.get_absolute_url()
            assert url == f'{expected_url}{bookcase_jetty.id},j,/'

    @pytest.mark.parametrize(
        ('lang', 'expected_url'),
        [
            ('da', '/da/mobel/bookcase/'),
            ('de', '/de/mobel/bookcase/'),
            ('en', '/en/furniture/bookcase/'),
            ('es', '/es/mueble/bookcase/'),
            ('fr', '/fr/meuble/bookcase/'),
            ('it', '/it/mobili/bookcase/'),
            ('nl', '/nl/meubel/bookcase/'),
            ('no', '/no/mobler/bookcase/'),
            ('pl', '/pl/furniture/bookcase/'),
            ('sv', '/sv/mobler/bookcase/'),
        ],
    )
    def test_get_item_url(self, bookcase_jetty, lang, expected_url):
        with translation.override(lang):
            url = bookcase_jetty.get_item_url()
            assert url == f'{expected_url}{bookcase_jetty.id},j,/'


@pytest.mark.django_db
class TestSotty:
    @pytest.mark.parametrize(
        ('lang', 'expected_url'),
        [
            ('da', '/da/configure/'),
            ('de', '/de/configure/'),
            ('en', '/en/configure/'),
            ('es', '/es/configure/'),
            ('fr', '/fr/configure/'),
            ('it', '/it/configure/'),
            ('nl', '/nl/configure/'),
            ('no', '/no/configure/'),
            ('pl', '/pl/configure/'),
            ('sv', '/sv/configure/'),
        ],
    )
    def test_get_absolute_url(self, sofa_sotty, lang, expected_url):
        with translation.override(lang):
            url = sofa_sotty.get_absolute_url()
            assert url == f'{expected_url}{sofa_sotty.id},s,/'

    @pytest.mark.parametrize(
        ('lang', 'expected_url'),
        [
            ('da', '/da/sofa/three_seater/'),
            ('de', '/de/sofa/three_seater/'),
            ('en', '/en/sofa/three_seater/'),
            ('es', '/es/sofa/three_seater/'),
            ('fr', '/fr/sofa/three_seater/'),
            ('it', '/it/divano/three_seater/'),
            ('nl', '/nl/bank/three_seater/'),
            ('no', '/no/sofa/three_seater/'),
            ('pl', '/pl/sofa/three_seater/'),
            ('sv', '/sv/soffa/three_seater/'),
        ],
    )
    @patch('gallery.models.Sotty.furniture_category', new_callable=PropertyMock)
    def test_get_item_url(self, property_mock, sofa_sotty, lang, expected_url):
        property_mock.return_value = FurnitureCategory.THREE_SEATER
        with translation.override(lang):
            url = sofa_sotty.get_item_url()
            assert url == f'{expected_url}{sofa_sotty.id},s,/'

    @pytest.mark.parametrize(
        ('lang', 'region_code', 'expected_url'),
        [
            ('da', 'dk', '/da-dk/sofa/tre_personers/'),
            ('de', 'de', '/de-de/sofa/drei_sitzer/'),
            ('en', 'uk', '/en-uk/sofa/three_seater/'),
            ('es', 'es', '/es-es/sofa/tres_plazas/'),
            ('fr', 'fr', '/fr-fr/sofa/trois_places/'),
            ('it', 'it', '/it-it/divano/tre_posti/'),
            ('nl', 'nl', '/nl-nl/bank/driezits/'),
            ('no', 'no', '/no-no/sofa/tre_seters/'),
            ('pl', 'pl', '/pl-pl/sofa/trzyosobowa/'),
            ('sv', 'se', '/sv-se/soffa/tre_sits/'),
        ],
    )
    @patch('gallery.models.Sotty.furniture_category', new_callable=PropertyMock)
    def test_get_item_url_with_region(
        self, property_mock, sofa_sotty, lang, region_code, expected_url, request
    ):
        property_mock.return_value = FurnitureCategory.THREE_SEATER
        region = request.getfixturevalue(f'region_{region_code}')
        with translation.override(lang):
            url = sofa_sotty.get_item_url_with_region(region=region)
            assert url == f'{expected_url}{sofa_sotty.id},s,/'

    @patch('gallery.models.models.create_sketch_image')
    @patch('gallery.models.models.transaction')
    def test_does_it_create_sketch(
        self, mock_transaction, mock_create_sketch, sotty_factory
    ):
        # Make transaction.on_commit execute the callback immediately
        mock_transaction.on_commit.side_effect = lambda callback: callback()
        sotty = sotty_factory()

        assert mock_create_sketch.delay.call_count == 1

        sotty.height = 100
        sotty.save()

        assert mock_create_sketch.delay.call_count == 1

    def test_does_it_create_gallery_desktop_image(
        self,
        sotty_factory,
    ):
        sotty = sotty_factory()
        images = FurnitureImage.objects.filter(
            furniture_content_type__model='sotty',
            furniture_object_id=sotty.id,
            type=FurnitureImageType.UNREAL_GALLERY_DESKTOP,
        )
        assert images.count() == 1

    def test_does_it_create_gallery_mobile_image(
        self,
        sotty_factory,
    ):
        sotty = sotty_factory()
        images = FurnitureImage.objects.filter(
            furniture_content_type__model='sotty',
            furniture_object_id=sotty.id,
            type=FurnitureImageType.UNREAL_GALLERY_MOBILE,
        )
        assert images.count() == 1

    @patch(
        'gallery.models.models.determine_sofa_category',
        return_value=FurnitureCategory.TWO_SEATER,
    )
    def test_does_it_call_determine_sofa_category(
        self, category_assigner_mock, sotty_factory
    ):
        sotty = sotty_factory()  # save called

        assert category_assigner_mock.call_count == 1

        sotty.pattern = 'dupa'
        sotty.save()

        assert category_assigner_mock.call_count == 1

        sotty.armrests = [{'geometry': 123}]
        sotty.save()

        assert category_assigner_mock.call_count == 1

        sotty.corners = [{'geometry': 123}]
        sotty.save(update_fields=['armrests'])

        assert category_assigner_mock.call_count == 2

    def test_set_material(self, sotty_factory):
        new_material = Sofa01Color.CORDUROY_BLUE_KLEIN

        def assert_material_fields(obj):
            for key in {'material', 'material_cushion', 'material_backrest'}:
                if key in obj:
                    obj[key] = new_material

        sotty = sotty_factory(materials=[Sofa01Color.REWOOL2_BROWN])

        sotty.set_material(material=new_material, with_save=False)

        assert sotty.materials == [Sofa01Color.CORDUROY_BLUE_KLEIN]
        assert_material_fields(sotty.configurator_params['material'])
        assert_material_fields(
            sotty.configurator_params['decoder_params']['default_material']
        )
        for obj in sotty.configurator_params['layout']:
            for module in obj['modules']:
                assert_material_fields(module)
        for geometry_field in Sotty.GEOMETRY_FIELDS:
            for module in getattr(sotty, geometry_field):
                if not module:
                    continue
                assert_material_fields(module)

    def test_ship_in_range_with_many_materials(self, sotty_factory):
        def mock_get_ship_in_range_for_shelf(color, *args, **kwargs):
            if color == Sofa01Color.REWOOL2_BROWN:
                return ShipInRangeChoice.WEEKS_5_6
            elif color == Sofa01Color.REWOOL2_OLIVE_GREEN:
                return ShipInRangeChoice.WEEKS_9_10
            return ShipInRangeChoice.WEEKS_2_3

        sotty = sotty_factory(
            materials=[Sofa01Color.REWOOL2_BROWN, Sofa01Color.REWOOL2_OLIVE_GREEN]
        )
        with patch.object(
            SottyShipInRangeMatrixService,
            'get_ship_in_range_for_shelf',
            side_effect=mock_get_ship_in_range_for_shelf,
        ):
            ship_in_range = sotty._get_ship_in_range()
            assert ship_in_range == ShipInRangeChoice.WEEKS_9_10


@pytest.mark.django_db
class TestWatty:

    # COLOR & MATERIALS
    def test_color_property(self, watty_factory):
        watty = watty_factory(material=Type03Color.GRAPHITE)
        assert watty.color == Type03Color.GRAPHITE

    def test_material_name_property(self, watty_factory):
        watty = watty_factory(material=Type03Color.BEIGE_PINK)
        assert watty.material_name == 'BEIGE_PINK'

    def test_get_material(self, watty_factory):
        watty = watty_factory(material=Type03Color.WHITE)
        assert watty.get_material() == ''

    # FEATURES
    @pytest.mark.parametrize(
        ('drawers', 'result'),
        [
            ([{'subtype': 'e'}], False),
            ([{'subtype': 'd'}], True),
            ([{'subtype': 'i'}], True),
            ([{'subtype': 'i'}, {'subtype': 'e'}], True),
        ],
    )
    def test_has_internal_drawers(self, watty_factory, drawers, result):
        watty = watty_factory(drawers=drawers)

        assert watty.has_internal_drawers is result

    @pytest.mark.parametrize(
        ('drawers', 'result'),
        [
            ([{'subtype': 'e'}], True),
            ([{'subtype': 'd'}], False),
            ([{'subtype': 'i'}], False),
            ([{'subtype': 'i'}, {'subtype': 'e'}], True),
            ([{'subtype': 'i'}, {'subtype': 'd'}], False),
        ],
    )
    def test_has_external_drawers(self, watty_factory, drawers, result):
        watty = watty_factory(drawers=drawers)

        assert watty.has_external_drawers is result

    @pytest.mark.parametrize(
        ('bars', 'result'),
        [
            ([{'x1': 9, 'x2': 620}, {'x1': 9, 'x2': 620}], True),
            ([{'x1': 9, 'x2': 620}, {'x1': 620, 'x2': 1231}], False),
        ],
    )
    def test_has_double_hangs(self, watty_factory, bars, result):
        watty = watty_factory(bars=bars)

        assert watty.has_double_hangs is result

    @pytest.mark.parametrize(
        ('bars', 'result'),
        [
            ([{'x1': 9, 'x2': 620, 'subtype': 'z'}], True),
            ([{'x1': 9, 'x2': 620}, {'x1': 620, 'x2': 1231}], False),
        ],
    )
    def test_has_front_facing_rail(self, watty_factory, bars, result):
        watty = watty_factory(bars=bars)

        assert watty.has_front_facing_rail is result

    def test_raises_error_when_trying_to_delete_watty_used_on_plp(
        self,
        watty,
        catalogue_entry_factory,
    ):
        catalogue_entry_factory(furniture=watty)
        with pytest.raises(GenericForeignKeyProtectedException):
            watty.delete()

    def test_raises_error_when_trying_to_delete_watties_used_on_plp(
        self,
        watty_factory,
        catalogue_entry_factory,
    ):
        watty_1 = watty_factory()
        watty_2 = watty_factory()
        catalogue_entry_factory(furniture=watty_1)
        with pytest.raises(GenericForeignKeyProtectedException):
            Watty.objects.filter(id__in={watty_1.id, watty_2.id}).delete()

    @pytest.mark.parametrize(
        ('lang', 'expected_url'),
        [
            ('da', '/da/mobel/wardrobe/'),
            ('de', '/de/mobel/wardrobe/'),
            ('en', '/en/furniture/wardrobe/'),
            ('es', '/es/mueble/wardrobe/'),
            ('fr', '/fr/meuble/wardrobe/'),
            ('it', '/it/mobili/wardrobe/'),
            ('nl', '/nl/meubel/wardrobe/'),
            ('no', '/no/mobler/wardrobe/'),
            ('pl', '/pl/furniture/wardrobe/'),
            ('sv', '/sv/mobler/wardrobe/'),
        ],
    )
    def test_get_absolute_url(self, wardrobe_watty, lang, expected_url):
        with translation.override(lang):
            url = wardrobe_watty.get_absolute_url()
            assert url == f'{expected_url}{wardrobe_watty.id},w,/'

    @pytest.mark.parametrize(
        ('lang', 'expected_url'),
        [
            ('da', '/da/mobel/wardrobe/'),
            ('de', '/de/mobel/wardrobe/'),
            ('en', '/en/furniture/wardrobe/'),
            ('es', '/es/mueble/wardrobe/'),
            ('fr', '/fr/meuble/wardrobe/'),
            ('it', '/it/mobili/wardrobe/'),
            ('nl', '/nl/meubel/wardrobe/'),
            ('no', '/no/mobler/wardrobe/'),
            ('pl', '/pl/furniture/wardrobe/'),
            ('sv', '/sv/mobler/wardrobe/'),
        ],
    )
    def test_get_item_url(self, wardrobe_watty, lang, expected_url):
        with translation.override(lang):
            url = wardrobe_watty.get_item_url()
            assert url == f'{expected_url}{wardrobe_watty.id},w,/'


@pytest.mark.django_db(transaction=True)
class TestSottyManager:
    """Test cases for SottyManager methods."""

    def test_get_single_modules_presets_basic_functionality(self, sotty_factory):
        """Test basic functionality of get_single_modules_presets method."""
        # Create a single module preset sotty
        sotty = sotty_factory(
            preset=True,
            covers_only=False,
            materials=[Sofa01Color.REWOOL2_BROWN],
            width=1000,
            depth=1625,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {
                                'type': SottyModuleType.CHAISE_LONGUE.value,
                                'width': 1000,
                                'depth': 1625,
                            }
                        ]
                    }
                ]
            },
        )

        # Call the method
        result = list(Sotty.objects.get_single_modules_presets(covers_only=False))

        # Verify the result structure
        assert len(result) == 1
        preset = result[0]

        # Check required fields are present
        expected_fields = {'id', 'material', 'width', 'depth', 'extra_depth', 'module_type'}
        assert set(preset.keys()) == expected_fields

        # Check values
        assert preset['id'] == sotty.id
        assert preset['material'] == Sofa01Color.REWOOL2_BROWN
        assert preset['width'] == 1000
        assert preset['depth'] == 1625
        assert preset['module_type'] == SottyModuleType.CHAISE_LONGUE.value
        assert preset['extra_depth'] is None  # Not an extended chaise longue

    def test_get_single_modules_presets_covers_only_filtering(self, sotty_factory):
        """Test filtering by covers_only parameter."""
        # Create cover preset
        cover_sotty = sotty_factory(
            preset=True,
            covers_only=True,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {
                                'type': SottyModuleType.SEATER.value,
                                'width': 750,
                                'depth': 1000,
                            }
                        ]
                    }
                ]
            },
        )

        # Create non-cover preset
        regular_sotty = sotty_factory(
            preset=True,
            covers_only=False,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {
                                'type': SottyModuleType.SEATER.value,
                                'width': 750,
                                'depth': 1000,
                            }
                        ]
                    }
                ]
            },
        )

        # Test covers_only=True
        covers_result = list(Sotty.objects.get_single_modules_presets(covers_only=True))
        assert len(covers_result) == 1
        assert covers_result[0]['id'] == cover_sotty.id

        # Test covers_only=False
        regular_result = list(Sotty.objects.get_single_modules_presets(covers_only=False))
        assert len(regular_result) == 1
        assert regular_result[0]['id'] == regular_sotty.id

    def test_get_single_modules_presets_module_type_filtering(self, sotty_factory):
        """Test filtering by module_type parameter."""
        # Create different module types
        seater_sotty = sotty_factory(
            preset=True,
            covers_only=False,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {
                                'type': SottyModuleType.SEATER.value,
                                'width': 750,
                                'depth': 1000,
                            }
                        ]
                    }
                ]
            },
        )

        chaise_sotty = sotty_factory(
            preset=True,
            covers_only=False,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {
                                'type': SottyModuleType.CHAISE_LONGUE.value,
                                'width': 875,
                                'depth': 1125,
                            }
                        ]
                    }
                ]
            },
        )

        # Test filtering by specific module type
        seater_result = list(
            Sotty.objects.get_single_modules_presets(
                covers_only=False, module_type=SottyModuleType.SEATER
            )
        )
        assert len(seater_result) == 1
        assert seater_result[0]['id'] == seater_sotty.id
        assert seater_result[0]['module_type'] == SottyModuleType.SEATER.value

        # Test filtering by different module type
        chaise_result = list(
            Sotty.objects.get_single_modules_presets(
                covers_only=False, module_type=SottyModuleType.CHAISE_LONGUE
            )
        )
        assert len(chaise_result) == 1
        assert chaise_result[0]['id'] == chaise_sotty.id
        assert chaise_result[0]['module_type'] == SottyModuleType.CHAISE_LONGUE.value

        # Test no module type filter (should return both)
        all_result = list(Sotty.objects.get_single_modules_presets(covers_only=False))
        assert len(all_result) == 2
        result_ids = {item['id'] for item in all_result}
        assert result_ids == {seater_sotty.id, chaise_sotty.id}

    def test_get_single_modules_presets_extended_chaise_longue_special_handling(self, sotty_factory):
        """Test special handling for EXTENDED_CHAISE_LONGUE module type."""
        # Create extended chaise longue with seaters data
        extended_sotty = sotty_factory(
            preset=True,
            covers_only=False,
            seaters=[
                {
                    'id': 'test_seater',
                    'depth': 1500,  # This should be used as extra_depth
                    'width': 875,
                    'material': Sofa01Color.REWOOL2_BROWN,
                }
            ],
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {
                                'type': SottyModuleType.EXTENDED_CHAISE_LONGUE.value,
                                'width': 875,
                                'depth': 1000,
                            }
                        ]
                    }
                ]
            },
        )

        result = list(
            Sotty.objects.get_single_modules_presets(
                covers_only=False, module_type=SottyModuleType.EXTENDED_CHAISE_LONGUE
            )
        )

        assert len(result) == 1
        preset = result[0]
        assert preset['id'] == extended_sotty.id
        assert preset['module_type'] == SottyModuleType.EXTENDED_CHAISE_LONGUE.value
        assert preset['depth'] == 1500  # Should come from seaters[0]['depth']

    def test_get_single_modules_presets_only_single_modules(self, sotty_factory):
        """Test that only single module presets are returned."""
        # Create single module preset (should be included)
        single_module_sotty = sotty_factory(
            preset=True,
            covers_only=False,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {
                                'type': SottyModuleType.SEATER.value,
                                'width': 750,
                                'depth': 1000,
                            }
                        ]
                    }
                ]
            },
        )

        # Create multi-module preset (should be excluded)
        sotty_factory(  # multi_module_sotty
            preset=True,
            covers_only=False,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {
                                'type': SottyModuleType.SEATER.value,
                                'width': 750,
                                'depth': 1000,
                            },
                            {
                                'type': SottyModuleType.ARMREST.value,
                                'width': 200,
                                'depth': 1000,
                            }
                        ]
                    }
                ]
            },
        )

        result = list(Sotty.objects.get_single_modules_presets(covers_only=False))

        # Should only return the single module preset
        assert len(result) == 1
        assert result[0]['id'] == single_module_sotty.id

    def test_get_single_modules_presets_only_presets(self, sotty_factory):
        """Test that only preset items are returned."""
        # Create preset item (should be included)
        preset_sotty = sotty_factory(
            preset=True,
            covers_only=False,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {
                                'type': SottyModuleType.SEATER.value,
                                'width': 750,
                                'depth': 1000,
                            }
                        ]
                    }
                ]
            },
        )

        # Create non-preset item (should be excluded)
        sotty_factory(  # non_preset_sotty
            preset=False,
            covers_only=False,
            configurator_params={
                'layout': [
                    {
                        'modules': [
                            {
                                'type': SottyModuleType.SEATER.value,
                                'width': 750,
                                'depth': 1000,
                            }
                        ]
                    }
                ]
            },
        )

        result = list(Sotty.objects.get_single_modules_presets(covers_only=False))

        # Should only return the preset item
        assert len(result) == 1
        assert result[0]['id'] == preset_sotty.id

    def test_get_single_modules_presets_empty_result(self):
        """Test method returns empty result when no matching data exists."""
        result = list(Sotty.objects.get_single_modules_presets(covers_only=False))
        assert len(result) == 0

        result_with_module_type = list(
            Sotty.objects.get_single_modules_presets(
                covers_only=False, module_type=SottyModuleType.SEATER
            )
        )
        assert len(result_with_module_type) == 0
